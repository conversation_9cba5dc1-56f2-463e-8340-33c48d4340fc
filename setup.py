import sys
import os
from cx_Freeze import setup, Executable

# --- 获取脚本的基本名称（不含.py） ---
script_name = os.path.splitext(os.path.basename(__file__))[0]

# --- 确定应用程序名称和版本 ---
app_name = "PhotonicBandCalculator"  # 应用程序名称
version = "1.0.0"  # 版本号

# --- 依赖项和包含文件 ---
# 列出你的程序明确依赖的Python包。cx_Freeze会自动检测一些，但最好明确列出。
packages = [
    "numpy",
    "scipy",
    "matplotlib",
    "tkinter",
    "multiprocessing",  # 如果你的代码使用了多进程
]

# 对于某些特定的模块，cx_Freeze可能需要一些额外的提示才能正确包含。
# 这通常是因为这些模块使用了动态导入或其他技巧。
includes = [
    "scipy.sparse.csgraph._shortest_path",
    "scipy.sparse.csgraph._tools",
    "scipy.sparse.linalg._dsolve",
    "scipy.sparse.linalg._eigen",
    "scipy.linalg.cython_blas",
    "scipy.linalg.cython_lapack",
    "matplotlib.backends.backend_tkagg", # 明确包含TkAgg后端
    "numpy.testing",
    "unittest"
]

# 如果你的程序依赖于标准库中不常用的模块，你可能需要将它们排除，以减小最终可执行文件的大小。
# 这是一个示例，你可以根据需要进行调整。
excludes = [
    "email",
    "html",
    "http",
    "xml",
    "unittest",
    "pydoc",
    "pdb",
]

# 如果你的应用程序需要任何数据文件（如图像、配置文件等），你需要将它们包含进来。
# include_files是一个列表，每个元素是一个元组(源文件路径, 目标文件路径)。
# 目标文件路径是相对于可执行文件的。
include_files = [
    # 示例：包含一个名为'data.txt'的数据文件，它将出现在与可执行文件相同的目录中。
    # ('data.txt', 'data.txt'),

    # 示例：包含一个名为'images'的目录及其所有内容。
    # ('images', 'images'),
]

# --- 构建选项 ---
build_exe_options = {
    "packages": packages,
    "includes": includes,
    "excludes": excludes,
    "include_files": include_files,
    # 优化级别 (0: 不优化, 1: 优化, 2: 更激进的优化)
    "optimize": 2,
     # 如果你想包含Python的DLL和pyd文件到一个单独的zip文件中，可以设置此选项。
    "include_msvcr": True, #可能需要包含微软的运行时库
}

# --- 可执行文件配置 ---
# 对于GUI应用程序，我们需要将base设置为"Win32GUI"，这将隐藏控制台窗口。
base = None
if sys.platform == "win32":
    base = "Win32GUI"

# 创建一个Executable对象，指定你的主脚本和一些选项。
executables = [
    Executable(
        "photonic_band_calculator.py",  # 你的主脚本文件
        base=base,                 # 对于GUI应用程序，设置为"Win32GUI"
        target_name=f"{app_name}.exe",  # 可执行文件的名称
        icon=None,                 # 可选：应用程序图标的路径 (例如: "icon.ico")
        #shortcut_name=f"{app_name} Shortcut",  # 可选: 快捷方式名称
        #shortcut_dir="DesktopFolder",           # 可选: 快捷方式创建位置
    )
]

# --- setup() 调用 ---
setup(
    name=app_name,
    version=version,
    description="FCC structure photonic band structure calculator",  # 描述
    options={"build_exe": build_exe_options},
    executables=executables,
)

# --- 构建步骤 ---
# 1.  将此文件保存为 `setup.py`，与你的 `photonic_band_calculator.py` 文件放在同一个目录中。
# 2.  打开命令行/终端。
# 3.  激活你的虚拟环境（如果你使用了虚拟环境）。
# 4.  导航到包含 `setup.py` 和 `photonic_band_calculator.py` 的目录。
# 5.  运行命令：  `python setup.py build`
# 6.  如果一切顺利，你将在 `build` 目录下找到一个包含可执行文件（以及其他必要文件）的文件夹。

# --- 可能的问题和解决方案 ---
# 1.  **运行错误：** 如果你遇到与缺失模块相关的错误，尝试将它们添加到 `includes` 列表中。
# 2.  **文件大小：** cx_Freeze 生成的可执行文件可能比较大。你可以尝试使用 UPX 压缩 (https://upx.github.io/) 来进一步减小文件大小。
# 3. **Tkinter 问题:** 确保你已经安装了正确版本的 Tkinter。有时候, 更新或重新安装 Tkinter 可以解决问题.