# FCC结构密堆积电介质球带结构计算器

## 项目概述
这是一个用于计算FCC（面心立方）结构密堆积电介质球光子带结构的Python应用程序。程序提供了图形用户界面，支持多种计算优化选项和结果可视化。

## 主要功能
- FCC结构光子带结构计算
- 图形用户界面（基于Tkinter）
- 多种矩阵对角化方法支持
- 并行计算支持
- 能带排序和平滑处理
- 结果保存和导出

## 文件结构
```
pwe-calculate/
├── photonic_band_calculator.py    # 主程序文件
├── requirements.txt               # Python依赖包列表
├── Photonic_Band_Calculator.spec  # PyInstaller配置文件
├── setup.py                      # cx_Freeze配置文件（备用）
├── .venv/                        # Python虚拟环境
├── dist/                         # PyInstaller生成的可执行文件
│   └── PhotonicBandCalculator/
│       └── PhotonicBandCalculator.exe
└── build/                        # 构建临时文件
```

## 环境要求
- Python 3.10+
- numpy >= 1.21.0
- scipy >= 1.7.0
- matplotlib >= 3.5.0
- numba >= 0.56.0
- pyinstaller >= 5.0.0

## 安装和使用

### 1. 虚拟环境设置
```bash
# 激活虚拟环境
.venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 运行程序
```bash
# 直接运行Python脚本
python photonic_band_calculator.py

# 或运行可执行文件
dist\PhotonicBandCalculator\PhotonicBandCalculator.exe
```

### 3. 重新生成可执行文件
```bash
# 使用PyInstaller
pyinstaller Photonic_Band_Calculator.spec

# 或使用cx_Freeze（备用方案）
python setup.py build
```

## 程序特性
- **材料参数设置**：电介质球半径、介电常数等
- **计算参数**：倒格矢量范围、能带数量、k点数量
- **高对称点路径**：支持自定义k空间路径
- **计算优化**：并行计算、稀疏矩阵、多种求解器
- **后处理**：能带排序、平滑处理
- **结果导出**：图像和数据保存

## 技术说明
- 使用平面波展开方法计算光子带结构
- 支持Numba加速计算
- 多进程并行计算支持
- 多种特征值求解算法（eigh, eigsh, lobpcg, arpack, davidson）

## 注意事项
1. 首次运行可能需要较长时间进行Numba编译
2. 大规模计算建议使用并行计算和稀疏矩阵优化
3. 可执行文件已包含所有必要的依赖库，可独立运行
