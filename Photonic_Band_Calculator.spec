# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_submodules, collect_data_files, collect_dynamic_libs

block_cipher = None

# --- Analysis ---
a = Analysis(
    ['photonic_band_calculator.py'],  # 你的主脚本
    pathex=[],
    binaries=[],
    datas=[
        # 使用 PyInstaller 的辅助函数来自动查找 mpl-data
        *collect_data_files('matplotlib', includes=['mpl-data/**']),
     ],
    hiddenimports=[
        'numpy', 'matplotlib', 'scipy', 'numba', 'tkinter', 'multiprocessing',
        'numpy.core._methods',
        'numpy.lib.format',
        'numpy.testing',  # 解决 unittest 错误
        'matplotlib.backends.backend_tkagg',
        'scipy.sparse.csgraph._validation',
        'scipy.special.cython_special',
        'scipy.linalg.cython_lapack',
        'scipy.linalg.cython_blas',
        'scipy.integrate._odepack', # 如果使用了scipy.integrate
        'scipy.integrate._quadpack',# 如果使用了scipy.integrate
        'tkinter.filedialog',
        'multiprocessing.pool',
        'multiprocessing.managers',
        'multiprocessing.popen_spawn_win32',  # Windows 特定
        'pkg_resources.py2_warn', # 可能出现的警告
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# --- PYZ (压缩的 Python 代码) ---
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# --- EXE (可执行文件) ---
exe = EXE(
    pyz,
    a.scripts,
    [],  # 清空 a.binaries, 让 PyInstaller 自动处理
    exclude_binaries=True, # 排除二进制文件，让它们在 COLLECT 中处理
    name='PhotonicBandCalculator',
    debug=False,  # 调试模式 (发布时改为 False)
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 使用 UPX 压缩 (如果已安装)
    console=False,  # 控制台窗口 (发布时改为 False)
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

# --- COLLECT (收集所有文件) ---
#  COLLECT 的作用是将所有依赖项 (包括可执行文件、动态库、数据文件等) 复制到一个单独的目录中。
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='PhotonicBandCalculator',
)

# --- multiprocessing 相关 (Windows) ---
# 在 Windows 上，如果使用了 multiprocessing，通常需要将以下代码添加到 EXE 配置之前：
if sys.platform.startswith('win'):
    from PyInstaller.utils.hooks import collect_data_files
    a.datas += collect_data_files('python*.dll')  # 包含 Python DLL