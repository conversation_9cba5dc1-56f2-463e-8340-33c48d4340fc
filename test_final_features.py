#!/usr/bin/env python3
"""
测试脚本：验证最终的功能修改
1. 实验单位制：输入球半径计算晶格常数
2. 英文界面标签
3. 改进的DOS计算（整个布里渊区）
"""

import numpy as np
import matplotlib.pyplot as plt
from photonic_band_calculator import (
    generate_fcc_reciprocal_lattice, 
    build_matrix, 
    solve_eigenvalue_problem
)

def test_sphere_to_lattice_conversion():
    """测试球半径到晶格常数的转换"""
    print("=== Sphere Radius to Lattice Constant Conversion ===")
    
    # 测试不同的球半径
    sphere_radii_nm = [100, 150, 177, 200, 250]  # nm
    
    print("Sphere Radius (nm) | Lattice Constant (nm) | Reduced Radius")
    print("-" * 60)
    
    for sphere_radius_nm in sphere_radii_nm:
        # FCC密堆积：ra = a/(2√2)，所以 a = ra × 2√2
        lattice_constant_nm = sphere_radius_nm * 2.0 * np.sqrt(2.0)
        
        # 约化半径 = 实际半径 / 晶格常数
        reduced_radius = sphere_radius_nm / lattice_constant_nm
        
        print(f"{sphere_radius_nm:15.1f} | {lattice_constant_nm:18.1f} | {reduced_radius:13.5f}")
    
    # 验证FCC密堆积条件
    print(f"\nFCC close-packed theoretical reduced radius: {1.0/(2.0*np.sqrt(2.0)):.5f}")
    print("All reduced radii should equal this theoretical value.")

def test_physical_unit_conversion():
    """测试物理单位转换"""
    print("\n=== Physical Unit Conversion Test ===")
    
    # 测试参数
    sphere_radius_nm = 177.0  # nm
    lattice_constant_nm = sphere_radius_nm * 2.0 * np.sqrt(2.0)
    
    # 约化频率范围
    reduced_frequencies = np.array([0.5, 1.0, 1.5, 2.0, 2.5])
    
    # 物理常数
    c = 2.998e8  # 光速 (m/s)
    a = lattice_constant_nm * 1e-9  # 晶格常数 (m)
    
    # 转换为THz
    physical_frequencies = reduced_frequencies * c / a / (2 * np.pi) / 1e12
    
    # 计算对应波长
    wavelengths_nm = c / (physical_frequencies * 1e12) * 1e9
    
    print(f"Sphere radius: {sphere_radius_nm} nm")
    print(f"Lattice constant: {lattice_constant_nm:.1f} nm")
    print(f"Conversion factor: {c / a / (2 * np.pi) / 1e12:.3f} THz per (ω/c)")
    print()
    print("Reduced Freq (ω/c) | Physical Freq (THz) | Wavelength (nm)")
    print("-" * 60)
    
    for i, (red_freq, phys_freq, wavelength) in enumerate(zip(reduced_frequencies, physical_frequencies, wavelengths_nm)):
        print(f"{red_freq:17.1f} | {phys_freq:17.2f} | {wavelength:13.1f}")

def test_dos_calculation_full_bz():
    """测试整个布里渊区的DOS计算"""
    print("\n=== Full Brillouin Zone DOS Calculation ===")
    
    # FCC密堆积参数
    ra = 1.0 / (2.0 * np.sqrt(2.0))
    epsilon_a = 11.56
    epsilon_b = 1.0
    f = 4*np.pi*(ra**3)/3
    n_max = 2  # 使用较小的值以加快测试
    num_bands = 4
    
    print(f"Parameters: ra={ra:.5f}, εa={epsilon_a}, f={f:.4f}")
    
    # 生成倒格矢量
    G_vectors = generate_fcc_reciprocal_lattice(n_max, use_cutoff=True)
    print(f"Using {len(G_vectors)} plane waves")
    
    # 在布里渊区内生成k点网格（较小的网格用于测试）
    nk = 4  # 每个方向的k点数量
    a = 2*np.pi
    
    k_points = []
    kx_range = np.linspace(-a/4, a/4, nk)  # 使用较小的范围
    ky_range = np.linspace(-a/4, a/4, nk)
    kz_range = np.linspace(-a/4, a/4, nk)
    
    for kx in kx_range:
        for ky in ky_range:
            for kz in kz_range:
                k_points.append(np.array([kx, ky, kz]))
    
    k_points = np.array(k_points)
    total_k_points = len(k_points)
    
    print(f"Computing DOS with {total_k_points} k-points...")
    
    # 计算所有k点的频率
    all_frequencies = []
    
    for i, k in enumerate(k_points):
        if i % 10 == 0:
            print(f"  Progress: {i+1}/{total_k_points}")
        
        # 构建矩阵
        H = build_matrix(k, G_vectors, ra, epsilon_a, epsilon_b, f)
        
        # 求解特征值
        eigenvalues = solve_eigenvalue_problem(H, num_bands, "eigh")
        frequencies = np.sqrt(eigenvalues)
        
        # 跳过零频率模式
        start_band = 1 if frequencies[0] < 1e-6 else 0
        valid_frequencies = frequencies[start_band:]
        
        all_frequencies.extend(valid_frequencies)
    
    all_frequencies = np.array(all_frequencies)
    
    print(f"Collected {len(all_frequencies)} frequency values")
    print(f"Frequency range: {np.min(all_frequencies):.3f} - {np.max(all_frequencies):.3f}")
    
    # 计算DOS
    freq_min = np.min(all_frequencies)
    freq_max = np.max(all_frequencies)
    freq_range = freq_max - freq_min
    
    freq_min -= freq_range * 0.05
    freq_max += freq_range * 0.05
    
    energy_grid = np.linspace(freq_min, freq_max, 500)
    
    # 使用高斯展宽计算DOS
    sigma = freq_range / 100
    dos = np.zeros_like(energy_grid)
    
    for freq in all_frequencies:
        dos += np.exp(-0.5 * ((energy_grid - freq) / sigma) ** 2)
    
    dos /= (sigma * np.sqrt(2 * np.pi))
    dos /= total_k_points
    
    print("DOS calculation completed")
    
    # 绘制DOS
    plt.figure(figsize=(8, 6))
    plt.plot(dos, energy_grid, 'k-', linewidth=2)
    plt.xlabel('Density of States')
    plt.ylabel('Frequency ω/c')
    plt.title('DOS from Full Brillouin Zone Sampling\n' + 
              f'FCC Close-Packed (ra={ra:.3f}, εa={epsilon_a})')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('dos_full_bz_test.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return energy_grid, dos

def test_english_labels():
    """测试英文标签"""
    print("\n=== English Label Test ===")
    
    labels = {
        "unit_system": "Unit System Selection",
        "reduced_units": "Reduced Units (ω/c)",
        "physical_units": "Physical Units (THz)",
        "sphere_radius": "Sphere Radius (nm)",
        "calculate_lattice": "Calculate Lattice",
        "calculated_lattice": "Calculated Lattice (nm)",
        "material_params": "FCC Close-Packed Material Parameters",
        "fcc_radius": "FCC Close-Packed Radius",
        "sphere_permittivity": "Sphere Permittivity (εa)",
        "background_permittivity": "Background Permittivity (εb)",
        "calculation_params": "Calculation Parameters",
        "g_vector_range": "G-vector Range (n_max)",
        "number_of_bands": "Number of Bands",
        "k_points_per_segment": "k-points per Segment",
        "calculate_dos": "Calculate DOS",
        "dos_energy_points": "DOS Energy Points",
        "high_symmetry_path": "High-Symmetry Point Path",
        "reset_path": "Reset to Default Path",
        "fcc_info": "FCC Close-Packed Information"
    }
    
    print("English labels for GUI elements:")
    for key, label in labels.items():
        print(f"{key:25s}: {label}")
    
    print("\nAll labels are in English to avoid font issues.")

if __name__ == "__main__":
    print("Final Features Test for FCC Photonic Crystal Calculator")
    print("=" * 60)
    
    try:
        # 测试球半径到晶格常数转换
        test_sphere_to_lattice_conversion()
        
        # 测试物理单位转换
        test_physical_unit_conversion()
        
        # 测试英文标签
        test_english_labels()
        
        # 测试DOS计算
        test_dos_calculation_full_bz()
        
        print("\n" + "=" * 60)
        print("All tests completed successfully!")
        print("\nKey improvements:")
        print("1. ✅ Experimental units: Input sphere radius → Calculate lattice constant")
        print("2. ✅ English labels to avoid font issues")
        print("3. ✅ DOS calculation over full Brillouin zone")
        print("4. ✅ Proper FCC close-packed parameter handling")
        print("\nDOS plot saved as 'dos_full_bz_test.png'")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
