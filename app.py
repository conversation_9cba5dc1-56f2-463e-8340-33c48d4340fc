import numpy as np
import matplotlib.pyplot as plt
from scipy.linalg import eigh
import tkinter as tk
from tkinter import ttk
import matplotlib
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import threading

matplotlib.use('TkAgg')

class PhotonicCrystalFCC:
    def __init__(self):
        # FCC晶格常数归一化为1
        self.a = 1.0
        
        # FCC原胞基矢
        self.a1 = np.array([0.5, 0.5, 0])
        self.a2 = np.array([0.5, 0, 0.5])
        self.a3 = np.array([0, 0.5, 0.5])
        
        # 计算倒格矢
        self.compute_reciprocal_vectors()
        
    def compute_reciprocal_vectors(self):
        """计算倒格矢"""
        V = np.dot(self.a1, np.cross(self.a2, self.a3))
        self.b1 = 2 * np.pi * np.cross(self.a2, self.a3) / V
        self.b2 = 2 * np.pi * np.cross(self.a3, self.a1) / V
        self.b3 = 2 * np.pi * np.cross(self.a1, self.a2) / V
        
    def get_high_symmetry_points(self):
        """获取FCC布里渊区的高对称点"""
        points = {
            'Γ': np.array([0, 0, 0]),
            'X': np.array([0.5, 0, 0.5]) * 2 * np.pi,
            'W': np.array([0.5, 0.25, 0.75]) * 2 * np.pi,
            'K': np.array([0.375, 0.375, 0.75]) * 2 * np.pi,
            'L': np.array([0.5, 0.5, 0.5]) * 2 * np.pi,
            'U': np.array([0.625, 0.25, 0.625]) * 2 * np.pi
        }
        return points
    
    def get_k_path(self, n_points=30):
        """生成高对称点之间的k点路径"""
        points = self.get_high_symmetry_points()
        path = ['Γ', 'X', 'W', 'K', 'Γ', 'L', 'U', 'W', 'L', 'K']
        
        k_points = []
        k_distances = []
        labels = []
        label_positions = []
        
        total_distance = 0
        
        for i in range(len(path) - 1):
            start = points[path[i]]
            end = points[path[i + 1]]
            
            # 记录起始点的标签位置
            if i == 0:
                labels.append(path[i])
                label_positions.append(total_distance)
            
            # 生成路径上的点
            segment_points = []
            for j in range(n_points):
                t = j / (n_points - 1)
                k_point = start + t * (end - start)
                segment_points.append(k_point)
            
            # 如果不是第一段，删除起始点避免重复
            if i > 0:
                segment_points = segment_points[1:]
            
            # 添加点并计算距离
            for j, k_point in enumerate(segment_points):
                k_points.append(k_point)
                
                if len(k_points) > 1:
                    distance = np.linalg.norm(k_points[-1] - k_points[-2])
                    total_distance += distance
                
                k_distances.append(total_distance)
            
            # 记录终点的标签位置
            labels.append(path[i + 1])
            label_positions.append(total_distance)
        
        return np.array(k_points), np.array(k_distances), labels, label_positions
    
    def get_G_vectors(self, n_max):
        """生成倒格矢G"""
        G_vectors = []
        for n1 in range(-n_max, n_max + 1):
            for n2 in range(-n_max, n_max + 1):
                for n3 in range(-n_max, n_max + 1):
                    G = n1 * self.b1 + n2 * self.b2 + n3 * self.b3
                    G_vectors.append(G)
        return np.array(G_vectors)
    
    def epsilon_fourier(self, G, r_sphere, eps_sphere, eps_background):
        """计算介电常数的傅里叶系数"""
        if np.linalg.norm(G) < 1e-10:
            # G = 0 的情况
            f = (4/3) * np.pi * r_sphere**3 / self.a**3
            return f * eps_sphere + (1 - f) * eps_background
        else:
            # G ≠ 0 的情况
            G_norm = np.linalg.norm(G)
            # FCC结构中每个原胞有4个原子
            atom_positions = [
                np.array([0, 0, 0]),
                np.array([0.5, 0.5, 0]),
                np.array([0.5, 0, 0.5]),
                np.array([0, 0.5, 0.5])
            ]
            
            structure_factor = 0
            for pos in atom_positions:
                structure_factor += np.exp(-1j * np.dot(G, pos))
            
            form_factor = (4 * np.pi * r_sphere**3 / self.a**3) * \
                         (np.sin(G_norm * r_sphere) - G_norm * r_sphere * np.cos(G_norm * r_sphere)) / \
                         (G_norm * r_sphere)**3
            
            return (eps_sphere - eps_background) * form_factor * structure_factor / 4
    
    def build_matrix(self, k_point, G_vectors, r_sphere, eps_sphere, eps_background, polarization='TE'):
        """构建本征值问题的矩阵"""
        n_G = len(G_vectors)
        matrix = np.zeros((n_G, n_G), dtype=complex)
        
        for i in range(n_G):
            for j in range(n_G):
                G_diff = G_vectors[i] - G_vectors[j]
                eps_G = self.epsilon_fourier(G_diff, r_sphere, eps_sphere, eps_background)
                
                if polarization == 'TE':
                    # TE模式
                    k_G_i = k_point + G_vectors[i]
                    k_G_j = k_point + G_vectors[j]
                    matrix[i, j] = np.dot(k_G_i, k_G_j) / eps_G
                else:
                    # TM模式
                    matrix[i, j] = np.linalg.norm(k_point + G_vectors[i])**2 / eps_G
        
        return matrix
    
    def calculate_band_structure(self, n_G_max, r_sphere, eps_sphere, eps_background, 
                               n_bands, n_k_points=30, polarization='TE'):
        """计算能带结构"""
        G_vectors = self.get_G_vectors(n_G_max)
        k_points, k_distances, labels, label_positions = self.get_k_path(n_k_points)
        
        bands = []
        
        for k_point in k_points:
            matrix = self.build_matrix(k_point, G_vectors, r_sphere, eps_sphere, 
                                     eps_background, polarization)
            eigenvalues, _ = eigh(matrix)
            eigenvalues = np.sqrt(np.abs(eigenvalues)) * self.a / (2 * np.pi)  # 转换为频率
            bands.append(eigenvalues[:n_bands])
        
        return np.array(bands).T, k_distances, labels, label_positions
    
    def calculate_dos(self, bands, n_bins=100):
        """计算态密度"""
        all_frequencies = bands.flatten()
        freq_min, freq_max = np.min(all_frequencies), np.max(all_frequencies)
        
        dos, bin_edges = np.histogram(all_frequencies, bins=n_bins, 
                                      range=(freq_min, freq_max), density=True)
        frequencies = (bin_edges[:-1] + bin_edges[1:]) / 2
        
        return frequencies, dos

class PhotonicCrystalGUI:
    def __init__(self, master):
        self.master = master
        self.master.title("FCC光子晶体能带结构计算器")
        self.master.geometry("1200x800")
        
        self.pc = PhotonicCrystalFCC()
        
        # 创建主框架
        self.create_widgets()
        
    def create_widgets(self):
        # 左侧参数面板
        param_frame = ttk.LabelFrame(self.master, text="参数设置", padding="10")
        param_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)
        
        # 参数输入
        ttk.Label(param_frame, text="球半径 (r/a):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.r_sphere_var = tk.DoubleVar(value=0.35)
        ttk.Entry(param_frame, textvariable=self.r_sphere_var, width=10).grid(row=0, column=1, pady=5)
        
        ttk.Label(param_frame, text="球介电常数:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.eps_sphere_var = tk.DoubleVar(value=12.0)
        ttk.Entry(param_frame, textvariable=self.eps_sphere_var, width=10).grid(row=1, column=1, pady=5)
        
        ttk.Label(param_frame, text="背景介电常数:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.eps_bg_var = tk.DoubleVar(value=1.0)
        ttk.Entry(param_frame, textvariable=self.eps_bg_var, width=10).grid(row=2, column=1, pady=5)
        
        ttk.Label(param_frame, text="平面波数量 (n_max):").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.n_G_max_var = tk.IntVar(value=3)
        ttk.Entry(param_frame, textvariable=self.n_G_max_var, width=10).grid(row=3, column=1, pady=5)
        
        ttk.Label(param_frame, text="能带数量:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.n_bands_var = tk.IntVar(value=10)
        ttk.Entry(param_frame, textvariable=self.n_bands_var, width=10).grid(row=4, column=1, pady=5)
        
        ttk.Label(param_frame, text="k点数量:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.n_k_points_var = tk.IntVar(value=20)
        ttk.Entry(param_frame, textvariable=self.n_k_points_var, width=10).grid(row=5, column=1, pady=5)
        
        ttk.Label(param_frame, text="极化:").grid(row=6, column=0, sticky=tk.W, pady=5)
        self.polarization_var = tk.StringVar(value="TE")
        polarization_combo = ttk.Combobox(param_frame, textvariable=self.polarization_var, 
                                        values=["TE", "TM"], width=8)
        polarization_combo.grid(row=6, column=1, pady=5)
        
        # 计算按钮
        ttk.Button(param_frame, text="计算", command=self.calculate).grid(row=7, column=0, 
                                                                         columnspan=2, pady=20)
        
        # 进度条
        self.progress = ttk.Progressbar(param_frame, mode='indeterminate')
        self.progress.grid(row=8, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.status_label = ttk.Label(param_frame, text="准备就绪")
        self.status_label.grid(row=9, column=0, columnspan=2, pady=5)
        
        # 右侧图形面板
        plot_frame = ttk.Frame(self.master)
        plot_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)
        
        # 创建matplotlib图形
        self.fig = Figure(figsize=(10, 8))
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 配置网格权重
        self.master.columnconfigure(1, weight=1)
        self.master.rowconfigure(0, weight=1)
        
    def calculate(self):
        """在单独的线程中运行计算"""
        self.progress.start()
        self.status_label.config(text="正在计算...")
        
        thread = threading.Thread(target=self.run_calculation)
        thread.start()
        
    def run_calculation(self):
        """执行计算"""
        try:
            # 获取参数
            r_sphere = self.r_sphere_var.get()
            eps_sphere = self.eps_sphere_var.get()
            eps_bg = self.eps_bg_var.get()
            n_G_max = self.n_G_max_var.get()
            n_bands = self.n_bands_var.get()
            n_k_points = self.n_k_points_var.get()
            polarization = self.polarization_var.get()
            
            # 计算能带结构
            bands, k_distances, labels, label_positions = self.pc.calculate_band_structure(
                n_G_max, r_sphere, eps_sphere, eps_bg, n_bands, n_k_points, polarization
            )
            
            # 计算态密度
            frequencies, dos = self.pc.calculate_dos(bands)
            
            # 在主线程中更新图形
            self.master.after(0, self.update_plots, bands, k_distances, labels, 
                            label_positions, frequencies, dos)
            
        except Exception as e:
            self.master.after(0, self.show_error, str(e))
            
    def update_plots(self, bands, k_distances, labels, label_positions, frequencies, dos):
        """更新图形"""
        self.fig.clear()
        
        # 绘制能带结构
        ax1 = self.fig.add_subplot(121)
        for band in bands:
            ax1.plot(k_distances, band, 'b-', linewidth=2)
        
        ax1.set_xlabel('波矢', fontsize=12)
        ax1.set_ylabel('频率 (ωa/2πc)', fontsize=12)
        ax1.set_title('能带结构', fontsize=14)
        ax1.grid(True, alpha=0.3)
        
        # 设置高对称点标签
        ax1.set_xticks(label_positions)
        ax1.set_xticklabels(labels)
        
        # 添加垂直线
        for pos in label_positions:
            ax1.axvline(x=pos, color='k', linestyle='--', alpha=0.3)
        
        # 绘制态密度
        ax2 = self.fig.add_subplot(122)
        ax2.plot(dos, frequencies, 'r-', linewidth=2)
        ax2.set_xlabel('态密度', fontsize=12)
        ax2.set_ylabel('频率 (ωa/2πc)', fontsize=12)
        ax2.set_title('态密度', fontsize=14)
        ax2.grid(True, alpha=0.3)
        
        # 设置y轴范围一致
        y_min = min(np.min(bands), np.min(frequencies))
        y_max = max(np.max(bands), np.max(frequencies))
        ax1.set_ylim(y_min, y_max)
        ax2.set_ylim(y_min, y_max)
        
        self.fig.tight_layout()
        self.canvas.draw()
        
        self.progress.stop()
        self.status_label.config(text="计算完成")
        
    def show_error(self, error_message):
        """显示错误信息"""
        self.progress.stop()
        self.status_label.config(text=f"错误: {error_message}")
        tk.messagebox.showerror("计算错误", f"计算过程中出现错误:\n{error_message}")

def main():
    root = tk.Tk()
    app = PhotonicCrystalGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
